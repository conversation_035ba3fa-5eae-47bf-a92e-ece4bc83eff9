<template>
	<view class="detail-container">
		<!-- 导航栏 -->
		<tui-navigation-bar
			:title="'详情'"
			:isCustom="true"
			backgroundColor="#ffffff"
			color="#333333"
		>
			<view class="custom-navbar">
				<view class="navbar-back" @click="goBack">
					<tui-icon name="arrowleft" :size="20" color="#333"></tui-icon>
				</view>
				<view class="navbar-title">详情</view>
				<view class="navbar-placeholder"></view>
			</view>
		</tui-navigation-bar>

		<!-- 内容区域 -->
		<view class="content-wrapper">
			<!-- 加载状态 -->
			<view v-if="loading" class="loading-container">
				<tui-loading :show="true" text="加载中..."></tui-loading>
			</view>

			<!-- 富文本内容 -->
			<view v-else-if="agreementContent" class="rich-text-container">
				<mp-html :content="agreementContent" :preview-img="true"></mp-html>
			</view>

			<!-- 无数据状态 -->
			<view v-else class="no-data-container">
				<tui-no-data text="暂无内容"></tui-no-data>
			</view>
		</view>
	</view>
</template>

<script>
	import tui from '../../../common/httpRequest';

	export default {
		data() {
			return {
				agreementId: '', // 协议ID
				agreementContent: '', // 协议内容
				loading: false // 加载状态
			};
		},
		onLoad(options) {
			// 获取传入的协议ID
			if (options.agreementId) {
				this.agreementId = options.agreementId;
				this.getDetailById();
			} else {
				tui.toast('参数错误');
				setTimeout(() => {
					this.goBack();
				}, 1500);
			}
		},
		methods: {
			// 获取详情数据
			getDetailById() {
				this.loading = true;
				tui.request('/api/admin/biz-agreement/getDetailById', 'post', {
					agreementId: this.agreementId
				}, false, false, false)
					.then(res => {
						this.loading = false;
						if (res.code === '0000') {
							this.agreementContent = res.data.agreementContent || '';
						} else {
							tui.toast(res.message || '获取详情失败');
						}
					})
					.catch(err => {
						this.loading = false;
						console.error('获取详情失败:', err);
						tui.toast('网络错误，请稍后重试');
					});
			},
			// 返回上一页
			goBack() {
				uni.navigateBack();
			}
		}
	};
</script>

<style lang="scss">
	.detail-container {
		min-height: 100vh;
		background-color: #f5f5f5;

		// 自定义导航栏样式
		.custom-navbar {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 44px;
			padding: 0 15px;

			.navbar-back {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 44px;
				height: 44px;
			}

			.navbar-title {
				flex: 1;
				text-align: center;
				font-size: 17px;
				font-weight: 500;
				color: #333;
			}

			.navbar-placeholder {
				width: 44px;
				height: 44px;
			}
		}

		.content-wrapper {
			padding: 20rpx;

			.loading-container {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 400rpx;
			}

			.rich-text-container {
				background-color: #fff;
				border-radius: 20rpx;
				padding: 30rpx;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

				// 富文本内容样式优化
				:deep(.mp-html) {
					line-height: 1.6;
					font-size: 28rpx;
					color: #333;

					p {
						margin-bottom: 20rpx;
					}

					img {
						max-width: 100%;
						height: auto;
						border-radius: 10rpx;
						margin: 20rpx 0;
					}

					h1, h2, h3, h4, h5, h6 {
						margin: 30rpx 0 20rpx 0;
						font-weight: bold;
					}

					h1 { font-size: 36rpx; }
					h2 { font-size: 34rpx; }
					h3 { font-size: 32rpx; }
					h4 { font-size: 30rpx; }
					h5 { font-size: 28rpx; }
					h6 { font-size: 26rpx; }
				}
			}

			.no-data-container {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 400rpx;
			}
		}
	}
</style>