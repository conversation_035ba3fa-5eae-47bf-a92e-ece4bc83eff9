<template>
	<view class="container">
		<tui-tips backgroundColor="red" color="#fff" :size="30" ref="toast"></tui-tips>
		<view class="form-area">
			<input class="tui-input" placeholder-class="phcolor" placeholder="请输入用户名" v-model="formData.username"
				maxlength="20" />
			<tui-white-space height="20"></tui-white-space>
			<input class="tui-input" placeholder-class="phcolor" placeholder="请输入密码" v-model="formData.password"
				maxlength="20" type="password" />
			<tui-white-space height="40"></tui-white-space>
			<tui-button type="primary" @click="login">登录</tui-button>
		</view>
	</view>
</template>

<script>
	import tui from '../../../common/httpRequest';
	export default {
		data() {
			return {
				formData: {
					username: '',
					password: ''
				}
			}
		},
		onLoad(option) {
			// 页面加载时的初始化逻辑（如果需要的话）
		},
		methods: {
			login() {
				// 验证输入
				if (!this.formData.username || !this.formData.password) {
					let options = {
						msg: '请输入用户名和密码',
						duration: 2000
					};
					this.$refs.toast.showTips(options);
					return;
				}

				// 构建请求参数
				const loginData = {
					username: this.formData.username,
					password: this.formData.password,
					clientId: "195da9fcce574852b850068771cde034",
					grantType: "password"
				};

				// 发起登录请求
				tui.request('/api/admin/auth/login', 'post', loginData, true, false, false).then((res) => {
					if (res.code === "0000") {
						// 将token存入本地缓存中
						uni.setStorageSync('token', res.data.accessToken);

						// 存储用户信息到Storage
						uni.setStorageSync('userInfo', res.data.userInfo);

						// 为了兼容可能的其他代码，也存储一些常用字段
						uni.setStorageSync('user', res.data.userInfo.nickname);
						uni.setStorageSync('userId', res.data.userInfo.id);

						//跳转到主页
						uni.switchTab({
							url: '../../tabbar/main/index'
						});
					} else {
						let options = {
							msg: res.message || '登录失败',
							duration: 2000
						};
						this.$refs.toast.showTips(options);
					}
				}).catch((res) => {
					console.log('登录请求失败:', res);
					let options = {
						msg: '网络错误，请稍后重试',
						duration: 2000
					};
					this.$refs.toast.showTips(options);
				})
			}
		}
	};
</script>

<style>
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 100vh;
		background-image: url('https://scwl.kingvisus.com/h5/static/images/login.png');
		background-size: cover;
		background-position: center;
	}

	.logo-area {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 50rpx;
	}

	.logo {
		width: 100rpx;
		height: 100rpx;
	}

	.app-name {
		font-size: 36rpx;
		color: #fff;
		margin-top: 20rpx;
	}

	.form-area {
		margin-top: 0rpx;
		width: 80%;
		padding: 40rpx;
		background-color: #fff;
		border-radius: 10rpx;
	}

	.tui-input {
		font-size: 30rpx;
		height: 88rpx;
		border: 1rpx solid #e6e6e6;
		border-radius: 4rpx;
		padding: 0 25rpx;
		box-sizing: border-box;
	}
</style>